<template>
  <el-dialog
    :title="config.title"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      :disabled="config.type === 'detail'"
    >
      <el-row :gutter="20">
        <!-- 活动编号 - 仅修改时显示 -->
        <el-col v-if="config.type === 'edit' || config.type === 'detail'" :span="24">
          <el-form-item label="活动编号:">
            <el-input v-model="formData.id" disabled style="max-width: 400px;" />
          </el-form-item>
        </el-col>

        <!-- 活动名称 -->
        <el-col :span="24">
          <el-form-item label="活动名称:" prop="actTitle">
            <el-input
              v-model="formData.actTitle"
              placeholder="请输入活动名称"
              maxlength="50"
              show-word-limit
              style="max-width: 600px;"
            />
          </el-form-item>
        </el-col>

        <!-- 活动标题 -->
        <el-col :span="24">
          <el-form-item label="活动标题:" prop="actCaption">
            <el-input
              v-model="formData.actCaption"
              placeholder="请输入活动标题"
              maxlength="50"
              show-word-limit
              style="max-width: 600px;"
            />
          </el-form-item>
        </el-col>

        <!-- 活动类型 -->
        <el-col :span="24">
          <el-form-item label="活动类型:" prop="typeTitle">
            <el-select
              v-model="formData.typeTitle"
              placeholder="请选择活动类型"
              style="max-width: 400px;"
              filterable
              :filter-method="filterActivityType"
              @clear="clearActivityTypeFilter"
            >
              <el-option
                v-for="item in filteredActivityTypeOptions"
                :key="item.typeId"
                :label="item.typeTitle"
                :value="item.typeTitle"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 头图 -->
        <el-col :span="24">
          <el-form-item label="头图:" prop="headerImg">
            <div class="image-upload-container">
              <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :headers="uploadHeaders"
                list-type="picture-card"
                :file-list="fileList"
                :on-success="handleImageSuccess"
                :on-remove="handleImageRemove"
                :on-preview="handleImagePreview"
                :before-upload="beforeImageUpload"
                :limit="5"
                multiple
                :disabled="config.type === 'detail'"
              >
                <i class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <!-- 图片加载状态遮罩 -->
              <div
                v-for="(file, index) in fileList"
                :key="file.uid"
                v-show="imageLoadingStates[file.uid]"
                class="image-loading-mask"
                :style="{ left: (index * 108) + 'px' }"
              >
                <i class="el-icon-loading"></i>
                <span>加载中...</span>
              </div>
            </div>
            <div class="form-tips">
              支持上传最多5张图片，支持常规的图片格式（jpg、JPEG、png、gif、svg），单张大小不超过500k
            </div>
          </el-form-item>
        </el-col>

        <!-- 排序 -->
        <el-col :span="24">
          <el-form-item label="排序:">
            <el-input-number
              v-model="formData.sort"
              :min="0"
              :max="9999"
              placeholder="默认为0"
              style="max-width: 200px;"
            />
          </el-form-item>
        </el-col>

        <!-- 活动门槛 -->
        <el-col :span="24">
          <el-form-item label="活动门槛:" prop="actThreshold">
            <el-checkbox-group v-model="thresholdList">
              <el-checkbox label="11">所有用户</el-checkbox>
              <el-checkbox label="00">00未评级客户</el-checkbox>
              <el-checkbox label="01">01普通客户</el-checkbox>
              <el-checkbox label="02">02潜力客户</el-checkbox>
              <el-checkbox label="03">03金卡客户</el-checkbox>
              <el-checkbox label="04">04白金卡客户</el-checkbox>
              <el-checkbox label="05">05钻石客户</el-checkbox>
              <el-checkbox label="06">06私人银行客户</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>

        <!-- 报名时间 -->
        <el-col :span="24">
          <el-form-item label="报名时间:" prop="registerTime">
            <el-date-picker
              v-model="registerTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="报名开始时间"
              end-placeholder="报名结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="max-width: 600px;"
              :default-time="['00:00:00', '23:59:59']"
            />
          </el-form-item>
        </el-col>

        <!-- 活动时间 -->
        <el-col :span="24">
          <el-form-item label="活动时间:" prop="activityTime">
            <el-date-picker
              v-model="activityTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="活动开始时间"
              end-placeholder="活动结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="max-width: 600px;"
              :default-time="['00:00:00', '23:59:59']"
            />
          </el-form-item>
        </el-col>

        <!-- 活动地点 -->
        <el-col :span="24">
          <el-form-item label="活动地点:" prop="location">
            <el-input
              v-model="formData.location"
              placeholder="请输入活动地点"
              maxlength="50"
              show-word-limit
              style="max-width: 600px;"
            />
          </el-form-item>
        </el-col>

        <!-- 详细地址 -->
        <el-col :span="24">
          <el-form-item label="详细地址:">
            <el-input
              v-model="formData.localDetail"
              placeholder="请输入详细地址"
              maxlength="50"
              show-word-limit
              style="max-width: 600px;"
            />
          </el-form-item>
        </el-col>

        <!-- 活动名额 -->
        <el-col :span="24">
          <el-form-item label="活动名额:" prop="numRage">
            <el-input-number
              v-model="formData.numRage"
              :min="0"
              :max="99999"
              placeholder="0表示不限制"
              style="max-width: 200px;"
            />
          </el-form-item>
        </el-col>

        <!-- 活动须知 -->
        <el-col :span="24">
          <el-form-item label="活动须知:" prop="actNotice">
            <el-input
              v-model="formData.actNotice"
              type="textarea"
              :rows="4"
              placeholder="请输入活动须知"
              maxlength="200"
              show-word-limit
              style="max-width: 800px;"
            />
          </el-form-item>
        </el-col>

        <!-- 活动介绍 -->
        <el-col :span="24">
          <el-form-item label="活动介绍:" prop="actDesc">
            <tinymce v-model="formData.actDesc" :height="300" placeholder="请输入活动介绍" />
          </el-form-item>
        </el-col>

        <!-- 报名填写项 -->
        <el-col :span="24">
          <el-form-item label="报名填写项:">
            <RegistrationForm v-model="registrationFormData" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ config.type === 'detail' ? '返回' : '取消' }}</el-button>
      <el-button
        v-if="config.type !== 'detail'"
        type="primary"
        :loading="saving"
        @click="handleSave"
      >保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addActivity, modifyActivity, getActivityType, getActivityDetail } from '@/api/actManage'
import { getToken } from '@/utils/auth'
import { getFileUrl } from '@/utils/common'
import RegistrationForm from './registrationForm.vue'
import Tinymce from '@/components/Tinymce/index.vue'

export default {
  name: 'ActivityDialog',
  components: {
    RegistrationForm,
    Tinymce
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 过滤后的活动类型选项
    filteredActivityTypeOptions() {
      if (!this.activityTypeFilterText) {
        return this.activityTypeOptions
      }
      return this.activityTypeOptions.filter(item => 
        item.typeTitle.toLowerCase().includes(this.activityTypeFilterText.toLowerCase())
      )
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      saving: false,
      activityTypeOptions: [],
      activityTypeFilterText: '', // 活动类型过滤文本
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/upload/file',
      uploadHeaders: {
        'X-Auth-Token': getToken() || ''
      },
      fileList: [],
      imageLoadingStates: {}, // 图片加载状态
      thresholdList: [],
      registerTime: [],
      activityTime: [],
      registrationFormData: {},
      formData: {
        id: null,
        actTitle: '',
        actCaption: '',
        typeTitle: '',
        headerImg: '',
        sort: 0,
        actThreshold: '',
        numRage: 0,
        location: '',
        localDetail: '',
        actNotice: '',
        actDesc: '',
        registerStartTime: '',
        registerEndTime: '',
        actStartTime: '',
        actEndTime: ''
      },
      rules: {
        actTitle: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        actCaption: [
          { required: true, message: '请输入活动标题', trigger: 'blur' }
        ],
        typeTitle: [
          { required: true, message: '请选择活动类型', trigger: 'change' }
        ],
        headerImg: [
          { required: true, message: '请上传头图', trigger: 'change' }
        ],
        actThreshold: [
          {
            required: true, 
            validator: (_, __, callback) => {
              if (!this.thresholdList || this.thresholdList.length === 0) {
                callback(new Error('请选择活动门槛'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        registerTime: [
          {
            required: true, 
            validator: (_, __, callback) => {
              if (!this.registerTime || this.registerTime.length !== 2) {
                callback(new Error('请选择报名时间'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        activityTime: [
          {
            required: true, 
            validator: (_, __, callback) => {
              if (!this.activityTime || this.activityTime.length !== 2) {
                callback(new Error('请选择活动时间'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        location: [
          { required: true, message: '请输入活动地点', trigger: 'blur' }
        ],
        numRage: [
          { required: true, message: '请输入活动名额', trigger: 'blur' }
        ],
        actNotice: [
          { required: true, message: '请输入活动须知', trigger: 'blur' }
        ],
        actDesc: [
          { required: true, message: '请输入活动介绍', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dialogVisible: {
      handler(val) {
        this.visible = val
        if (val) {
          this.initData()
        }
      },
      immediate: true
    },
    thresholdList: {
      handler(val) {
        this.formData.actThreshold = val.join(',')
      },
      deep: true
    },
    registerTime: {
      handler(val) {
        if (val && val.length === 2) {
          this.formData.registerStartTime = val[0]
          this.formData.registerEndTime = val[1]
        }
      },
      deep: true
    },
    activityTime: {
      handler(val) {
        if (val && val.length === 2) {
          this.formData.actStartTime = val[0]
          this.formData.actEndTime = val[1]
        }
      },
      deep: true
    }
  },
  created() {
    this.loadActivityTypes()
  },
  mounted() {
    if (this.dialogVisible) {
      this.initData()
    }
  },
  methods: {
    // 初始化数据
    initData() {
      if (this.config.formData) {
        // 详情、编辑、复制都需要获取完整的活动详情
        if (this.config.type === 'detail' || this.config.type === 'edit' || this.config.isCopy) {
          this.getActivityDetail()
        } else {
          // 新增时使用传入的数据或重置表单
          this.setFormData(this.config.formData)
        }
      } else {
        this.resetForm()
      }
    },

    // 获取活动详情
    async getActivityDetail() {
      try {
        console.log('获取活动详情参数:', this.config.formData)
        const res = await getActivityDetail(this.config.formData.id)
        if (res.code === 200 && res.data) {
          const data = { ...res.data }

          // 如果是复制操作，清空ID相关字段
          if (this.config.isCopy) {
            data.id = null
            data.createId = null
            data.createTime = null
            data.updateId = null
            data.updateTime = null
            data.versionCt = null
          }

          this.setFormData(data)
        } else {
          this.$message.error('获取活动详情失败')
          this.handleClose()
        }
      } catch (error) {
        console.error('获取活动详情失败:', error)
        this.$message.error('获取活动详情失败，请重试')
        this.handleClose()
      }
    },

    // 设置表单数据
    setFormData(data) {
      this.formData = { ...data }

      // 处理活动门槛
      if (this.formData.actThreshold) {
        this.thresholdList = this.formData.actThreshold.split(',')
      }

      // 处理时间 - 转换ISO格式为Element UI期望的格式
      if (this.formData.registerStartTime && this.formData.registerEndTime) {
        const registerStart = this.formatDateTime(this.formData.registerStartTime)
        const registerEnd = this.formatDateTime(this.formData.registerEndTime)
        this.registerTime = [registerStart, registerEnd]
      }
      if (this.formData.actStartTime && this.formData.actEndTime) {
        const actStart = this.formatDateTime(this.formData.actStartTime)
        const actEnd = this.formatDateTime(this.formData.actEndTime)
        this.activityTime = [actStart, actEnd]
      }

      // 处理图片（支持多张图片，用逗号分隔）
      if (this.formData.headerImg) {
        const imageUrls = this.formData.headerImg.split(',')

        // 先清空图片加载状态
        this.imageLoadingStates = {}

        this.fileList = imageUrls.map((url, index) => {
          const uid = Date.now() + index
          const fullUrl = getFileUrl(url.trim())

          // 设置图片加载状态
          this.imageLoadingStates[uid] = true

          // 预加载图片以减少跳动
          this.preloadImage(fullUrl, uid)

          return {
            name: `image_${index}`,
            url: fullUrl,
            uid: uid,
            response: {
              code: 200,
              data: {
                url: url.trim()
              }
            }
          }
        })
      }

      // 处理报名填写项
      if (this.formData.registerInfo) {
        try {
          const parsedData = JSON.parse(this.formData.registerInfo)

          // 如果解析后是数组，需要转换为对象格式
          if (Array.isArray(parsedData)) {
            this.registrationFormData = this.convertArrayToFormData(parsedData)
          } else if (parsedData && typeof parsedData === 'object') {
            this.registrationFormData = parsedData
          } else {
            this.registrationFormData = {}
          }
        } catch (error) {
          console.error('解析报名信息失败:', error)
          this.registrationFormData = {}
        }
      } else {
        this.registrationFormData = {}
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        actTitle: '',
        actCaption: '',
        typeTitle: '',
        headerImg: '',
        sort: 0,
        actThreshold: '',
        numRage: 0,
        location: '',
        localDetail: '',
        actNotice: '',
        actDesc: '',
        registerStartTime: '',
        registerEndTime: '',
        actStartTime: '',
        actEndTime: ''
      }
      this.thresholdList = []
      this.registerTime = []
      this.activityTime = []
      this.fileList = []
      this.imageLoadingStates = {}
      this.registrationFormData = {}
    },

    // 加载活动类型
    async loadActivityTypes() {
      try {
        const res = await getActivityType({
          delFlag: '0',
          actTypeStatus: 1
        })
        if (res.code === 200) {
          this.activityTypeOptions = res.data.list || []
        }
      } catch (error) {
        console.error('加载活动类型失败:', error)
      }
    },

    // 活动类型过滤方法
    filterActivityType(query) {
      this.activityTypeFilterText = query
    },

    // 清除活动类型过滤
    clearActivityTypeFilter() {
      this.activityTypeFilterText = ''
    },

    // 图片上传前验证
    beforeImageUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'].includes(file.type)
      const isLt500K = file.size / 1024 < 500

      if (!isValidType) {
        this.$message.error('只能上传 JPG、PNG、JPEG、GIF、SVG 格式的图片!')
        return false
      }
      if (!isLt500K) {
        this.$message.error('图片大小不能超过 500KB!')
        return false
      }
      return true
    },

    // 图片上传成功（多图）
    handleImageSuccess(response, file, fileList) {
      if (response.code === 200) {
        // 将返回的可访问地址写入当前文件，便于预览
        file.response = response
        file.url = getFileUrl(response.data.url)

        // 维护绑定的文件列表
        this.fileList = fileList

        // 更新所有图片URL（用逗号拼接）
        this.updateHeaderImgUrls()
        this.$message.success('上传成功')
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },

    // 图片移除
    handleImageRemove(file, fileList) {
      // 清理对应的加载状态
      if (file && file.uid && this.imageLoadingStates[file.uid] !== undefined) {
        this.$delete(this.imageLoadingStates, file.uid)
      }

      this.fileList = fileList
      // 更新所有图片URL（用逗号拼接）
      this.updateHeaderImgUrls()
    },

    // 更新头图URL（将所有图片URL用逗号拼接）
    updateHeaderImgUrls() {
      if (!this.fileList || this.fileList.length === 0) {
        this.formData.headerImg = ''
        return
      }
      
      const urls = []
      
      this.fileList.forEach(file => {
        if (file.response && file.response.code === 200) {
          urls.push(file.response.data.url)
        }
      })
      
      this.formData.headerImg = urls.join(',')
    },

    // 图片预览
    handleImagePreview(file) {
      const url = file && (file.url || (file.response && getFileUrl(file.response.data.url)))
      if (url) {
        window.open(url, '_blank')
      }
    },

    // 获取图片完整URL
    getImageUrl(url) {
      return getFileUrl(url)
    },

    // 预加载图片以减少跳动
    preloadImage(url, uid) {
      const img = new Image()
      img.onload = () => {
        // 图片加载完成，更新状态
        this.$set(this.imageLoadingStates, uid, false)
      }
      img.onerror = () => {
        // 图片加载失败，也更新状态
        this.$set(this.imageLoadingStates, uid, false)
      }
      img.src = url
    },

    // 验证时间
    validateTime() {
      if (this.registerTime.length === 2 && this.activityTime.length === 2) {
        // 直接比较时间字符串，避免时区转换问题
        const registerEnd = this.registerTime[1]
        const activityStart = this.activityTime[0]

        if (registerEnd > activityStart) {
          this.$message.error('活动开始时间不得早于报名结束时间，请重新设置')
          return false
        }
      }
      return true
    },

    // 保存
    handleSave() {
      this.$refs.form.validate(async(valid) => {
        if (!valid) return

        if (!this.validateTime()) return

        // 验证图片是否已上传
        if (!this.formData.headerImg || !this.fileList || this.fileList.length === 0) {
          this.$message.error('请先上传头图')
          return
        }

        this.saving = true
        try {
          const isEdit = this.config.type === 'edit'
          const apiMethod = isEdit ? modifyActivity : addActivity

          const submitData = { ...this.formData }

          // 处理报名填写项数据
          if (this.registrationFormData && Object.keys(this.registrationFormData).length > 0) {
            submitData.activityUserDTOs = this.convertToactivityUserDTOs(this.registrationFormData)
          }

          // 在提交前将时间格式转换为ISO格式
          if (this.registerTime && this.registerTime.length > 0) {
            submitData.registerStartTime = this.formatToISOString(this.registerTime[0])
            submitData.registerEndTime = this.formatToISOString(this.registerTime[1])
          }
          if (this.activityTime && this.activityTime.length > 0) {
            submitData.actStartTime = this.formatToISOString(this.activityTime[0])
            submitData.actEndTime = this.formatToISOString(this.activityTime[1])
          }

          // // 移除不需要的字段
          // delete submitData.registerInfo
          // delete submitData.location  // 接口文档中没有这个字段

          // 新增时不传 id
          if (!isEdit) {
            delete submitData.id
          }

          const res = await apiMethod(submitData)
          if (res.code === 200) {
            this.$message.success(isEdit ? '修改成功' : '新增成功')
            this.$emit('saveSuccess')
            this.handleClose()
          } else {
            this.$message.error(res.msg || (isEdit ? '修改失败' : '新增失败'))
          }
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error('保存失败，请重试')
        } finally {
          this.saving = false
        }
      })
    },

    // 转换报名信息为接口格式
    convertToactivityUserDTOs(registrationData) {
      const activityUserDTOs = []

      // 处理系统预设项目
      if (registrationData.presetItems) {
        const presetItems = registrationData.presetItems
        const fieldMapping = {
          name: { title: '姓名', key: 'name', type: 0 },
          mobile: { title: '手机号', key: 'mobile', type: 0 },
          idNo: { title: '身份证', key: 'idNo', type: 0 },
          age: { title: '年龄', key: 'age', type: 0 },
          gender: { title: '性别', key: 'gender', type: 2 },
          cardType: { title: '证件类型', key: 'cardType', type: 2 },
          relative: { title: '携带亲属', key: 'relative', type: 0 },
          height: { title: '身高', key: 'height', type: 0 },
          weight: { title: '体重', key: 'weight', type: 0 },
          education: { title: '学历', key: 'education', type: 2 }
        }

        Object.keys(fieldMapping).forEach(key => {
          const item = presetItems[key]
          if (item && item.enabled) {
            const dto = {
              title: fieldMapping[key].title,
              key: fieldMapping[key].key,
              type: fieldMapping[key].type,
              required: item.required
            }

            // 处理选项类型的字段
            if (item.options && Array.isArray(item.options)) {
              dto.text = item.options.join(',')
            }

            // 处理携带亲属的特殊字段
            if (key === 'relative') {
              if (item.adultLimit !== null && item.adultLimit !== undefined) {
                dto.human = item.adultLimit
              }
              if (item.childLimit !== null && item.childLimit !== undefined) {
                dto.child = item.childLimit
              }
            }

            activityUserDTOs.push(dto)
          }
        })
      }

      // 处理自增项目
      if (registrationData.customItems && Array.isArray(registrationData.customItems)) {
        registrationData.customItems.forEach((item, index) => {
          if (item.enabled) {
            const dto = {
              title: item.name,
              key: `custom_${index + 1}_${item.componentType}`, // 生成唯一key
              type: this.getTypeByComponentType(item.componentType),
              required: item.required
            }

            // 处理选项
            if (item.options && Array.isArray(item.options)) {
              dto.text = item.options.join(',')
            }

            activityUserDTOs.push(dto)
          }
        })
      }

      return activityUserDTOs
    },

    // 根据组件类型获取type值
    getTypeByComponentType(componentType) {
      const typeMapping = {
        text: 0,      // 单行文本
        textarea: 1,  // 多行文本
        select: 2     // 单选下拉框
      }
      return typeMapping[componentType] || 0
    },

    // 将数组格式的报名信息转换为对象格式
    convertArrayToFormData(arrayData) {
      const formData = {
        presetItems: {
          name: { enabled: true, required: true },
          mobile: { enabled: true, required: true },
          idNo: { enabled: false, required: false },
          age: { enabled: false, required: false },
          gender: { enabled: false, required: false, options: ['男', '女', '其他'] },
          cardType: { enabled: false, required: false, options: ['身份证', '护照', '其他'] },
          relative: { enabled: false, required: false, adultLimit: null, childLimit: null },
          height: { enabled: false, required: false },
          weight: { enabled: false, required: false },
          education: { enabled: false, required: false, options: ['初中', '高中', '大专', '本科', '硕士', '博士', '其他'] }
        },
        customItems: []
      }

      // 遍历数组数据，映射到对应的字段
      arrayData.forEach(item => {
        const { key, title, type, required, text, human, child } = item

        // 处理系统预设项目
        const presetMapping = {
          name: 'name',
          mobile: 'mobile',
          idNo: 'idNo',
          age: 'age',
          gender: 'gender',
          cardType: 'cardType',
          relative: 'relative',
          height: 'height',
          weight: 'weight',
          education: 'education'
        }

        const presetKey = presetMapping[key]
        if (presetKey && formData.presetItems[presetKey]) {
          formData.presetItems[presetKey].enabled = true
          formData.presetItems[presetKey].required = required || false

          // 处理选项类型的字段
          if (text && ['gender', 'cardType', 'education'].includes(presetKey)) {
            formData.presetItems[presetKey].options = text.split(',')
          }

          // 处理携带亲属的特殊字段
          if (presetKey === 'relative') {
            if (human !== null && human !== undefined) {
              formData.presetItems[presetKey].adultLimit = human
            }
            if (child !== null && child !== undefined) {
              formData.presetItems[presetKey].childLimit = child
            }
          }
        } else if (key && key.startsWith('custom_')) {
          // 处理自定义项目
          const componentType = type === 0 ? 'text' : type === 1 ? 'textarea' : 'select'
          formData.customItems.push({
            name: title,
            componentType: componentType,
            required: required || false,
            enabled: true,
            options: text ? text.split(',') : []
          })
        }
      })

      return formData
    },

    // 格式化日期时间 - 将后端ISO格式转换为Element UI期望的格式
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''
      
      try {
        // 如果已经是 "yyyy-MM-dd HH:mm:ss" 格式，直接返回
        if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateTimeStr)) {
          return dateTimeStr
        }
        
        // 如果是 "yyyy-MM-ddTHH:mm:ss" 格式，转换为 "yyyy-MM-dd HH:mm:ss"
        if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(dateTimeStr)) {
          return dateTimeStr.replace('T', ' ')
        }
        
        // 如果是完整的ISO格式或其他格式，使用Date对象转换
        const date = new Date(dateTimeStr)
        if (isNaN(date.getTime())) {
          return dateTimeStr
        }
        
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      } catch (error) {
        console.error('时间格式转换失败:', error)
        return dateTimeStr
      }
    },
    
    // Element UI格式转ISO格式 - 转换为后端需要的格式：2025-08-15T10:49:14
    formatToISOString(time) {
      if (!time) return ''
      
      // 将 "yyyy-MM-dd HH:mm:ss" 格式转换为 "yyyy-MM-ddTHH:mm:ss" 格式
      // 这样既满足后端需求，又保持本地时间不变
      return time.replace(' ', 'T')
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

::v-deep .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.el-checkbox-group {
  .el-checkbox {
    margin-right: 15px;
    margin-bottom: 10px;
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}

.avatar {
  width: 148px;
  height: 148px;
  display: block;
}

.form-tips {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
  line-height: 1.4;
}

.image-upload-container {
  position: relative;

  .image-loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 6px;
    border: 1px solid #d9d9d9;

    i {
      font-size: 20px;
      color: #409EFF;
      margin-bottom: 5px;
    }

    span {
      font-size: 12px;
      color: #666;
    }
  }
}
</style>
