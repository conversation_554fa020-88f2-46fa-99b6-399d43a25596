<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        >
          <template #addBtn>
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleAddOrEdit('add')"
            >新增</el-button>
          </template>
          <template #exportBtn>
            <el-button
              type="success"
              icon="el-icon-download"
              @click="handleExport"
            >导出</el-button>
          </template>
        </dynamic-query>
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template #actStatusText="{ rowData }">
              {{ actStatusMap[rowData.actStatus] }}
          </template>
          <template #activityStatusText="{ rowData }">
            {{ rowData.activityStatus }}
          </template>
          <template #registerStatusText="{ rowData }">
            {{ rowData.registerStatus }}
          </template>
          <template #actThresholdText="{ rowData }">
            {{ rowData.actThreshold }}
          </template>
          <template v-slot:operate="{ rowData }">
            <el-button
              v-if="rowData.actStatus !== 2"
              type="text"
              @click.stop="handleSignCode(rowData)"
            >活动签到码</el-button>
            <el-button
              type="text"
              @click.stop="handleAddOrEdit('detail', rowData)"
            >详情</el-button>
            <el-button
              v-if="[2, 0].includes(rowData.actStatus)"
              type="text"
              @click.stop="handleAddOrEdit('edit', rowData)"
            >修改</el-button>
            <el-button
              v-if="[2, 0].includes(rowData.actStatus)"
              type="text"
              style="color: #67c23a"
              @click.stop="handleOnSale(rowData)"
            >上架</el-button>
            <el-button
              v-if="rowData.actStatus === 1"
              type="text"
              style="color: #e6a23c"
              @click.stop="handleDownSale(rowData)"
            >下架</el-button>
            <el-button
              type="text"
              @click.stop="handleAddOrEdit('copy', rowData)"
            >复制</el-button>
            <el-button
              v-if="[2, 0].includes(rowData.actStatus)"
              type="text"
              style="color: #f56c6c"
              @click.stop="handleDelete(rowData)"
            >删除</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>

    <!-- 活动签到码弹窗 -->
    <el-dialog
      title="活动签到码"
      :visible.sync="signCodeVisible"
      width="500px"
      center
    >
      <div class="sign-code-content">
        <div class="qr-code-container">
          <div ref="qrcode" class="qr-code" />
        </div>
        <div class="activity-info">
          <p><strong>活动签到码：</strong>{{ currentActivity.signCode }}</p>
          <p><strong>活动编号：</strong>{{ currentActivity.id }}</p>
          <p><strong>活动名称：</strong>{{ currentActivity.actTitle }}</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="signCodeVisible = false">返回</el-button>
        <el-button type="primary" @click="downloadSignCode">下载</el-button>
      </div>
    </el-dialog>

    <!-- 配置dialog -->
    <ActivityDialog
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :config="dialogConfig"
      @handleClose="handleClose"
      @saveSuccess="saveSuccess"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import ActivityDialog from './components/activityDialog.vue'
import {
  onSaleActivity,
  downSaleActivity,
  deleteActivity,
  exportActivityExcel,
  getActivityType,
  exportAndDownloadActivity
} from '@/api/actManage'
import { getTableList } from '@/api/common'
import { downOrViewFile, formatDates } from '@/utils'
import QRCode from 'qrcode'
import html2canvas from 'html2canvas'

// 状态映射
const actStatusMap = {
  0: '已下架',
  1: '已上架',
  2: '草稿'
}

export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    ActivityDialog
  },
  data() {
    return {
      tableLoading: false,
      queryData: {},
      dialogVisible: false,
      dialogConfig: {},
      signCodeVisible: false,
      currentActivity: {},
      activityTypeOptions: [],
      queryConfig: {
        queryItem: [
          {
            type: 'text',
            label: '活动编号:',
            model: 'id',
            clearable: true,
            size: 'small'
          },
          {
            type: 'text',
            label: '活动名称:',
            model: 'actTitle',
            clearable: true,
            size: 'small'
          },
          {
            type: 'select',
            label: '活动类型:',
            model: 'typeTitle',
            optionType: 'list',
            optionValue: 'typeTitle',
            optionLabel: 'typeTitle',
            optionList: [],
            filterable: true
          },
          {
            type: 'select',
            label: '活动门槛:',
            model: 'actThreshold',
            optionType: 'list',
            optionValue: 'value',
            optionLabel: 'label',
            optionList: [
              { value: '', label: '所有用户' },
              { value: '00', label: '00未评级客户' },
              { value: '01', label: '01普通客户' },
              { value: '02', label: '02潜力客户' },
              { value: '03', label: '03金卡客户' },
              { value: '04', label: '04白金卡客户' },
              { value: '05', label: '05钻石客户' },
              { value: '06', label: '06私人银行客户' }
            ]
          },
          {
            type: 'select',
            label: '上架状态:',
            model: 'actStatus',
            optionType: 'list',
            optionValue: 'value',
            optionLabel: 'label',
            optionList: [
              { value: 2, label: '草稿' },
              { value: 1, label: '已上架' },
              { value: 0, label: '已下架' }
            ]
          },
          {
            type: 'select',
            label: '活动状态:',
            model: 'activityStatus',
            optionType: 'list',
            optionValue: 'value',
            optionLabel: 'label',
            optionList: [
              { value: '1', label: '活动未开始' },
              { value: '2', label: '活动进行中' },
              { value: '3', label: '活动已结束' }
            ]
          },
          {
            type: 'select',
            label: '报名状态:',
            model: 'registerStatus',
            optionType: 'list',
            optionValue: 'value',
            optionLabel: 'label',
            optionList: [
              { value: '1', label: '报名未开始' },
              { value: '2', label: '报名进行中' },
              { value: '3', label: '报名已结束' }
            ]
          },
          {
            type: 'date',
            label: '创建时间:',
            model: 'createTimeArr',
            config: {
              type: 'datetimerange',
              format: 'yyyy-MM-dd HH:mm:ss',
              separator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              defaultTime: ['00:00:00', '23:59:59']
            }
          },
          {
            type: 'option'
          },
          {
            type: 'option',
            slotName: 'addBtn'
          },
          {
            type: 'option',
            slotName: 'exportBtn'
          }
        ]
      },
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true,
            width: 60
          },
          {
            prop: 'id',
            label: '活动编号',
            width: 100
          },
          {
            prop: 'actTitle',
            label: '活动名称',
            minWidth: 150
          },
          {
            prop: 'actCaption',
            label: '活动标题',
            minWidth: 150
          },
          {
            prop: 'sort',
            label: '排序',
            width: 80
          },
          {
            prop: 'typeTitle',
            label: '活动类型',
            width: 120
          },
          {
            prop: 'actThreshold',
            label: '活动门槛',
            slotName: 'actThresholdText',
            width: 120
          },
          {
            prop: 'numRage',
            label: '活动名额',
            width: 100
          },
          {
            prop: 'actStatus',
            label: '上架状态',
            slotName: 'actStatusText',
            width: 100
          },
          {
            prop: 'activityStatus',
            label: '活动状态',
            slotName: 'activityStatusText',
            width: 120
          },
          {
            prop: 'registerStatus',
            label: '报名状态',
            slotName: 'registerStatusText',
            width: 120
          },
          {
            prop: 'createTime',
            label: '创建时间',
            width: 160
          },
          {
            prop: 'updateTime',
            label: '更新时间',
            width: 160
          },
          {
            prop: 'updateId',
            label: '更新者',
            width: 100
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate',
            fixed: 'right',
            minWidth: 300
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      queryUrl: '/act-manage/activity/getActivityList',
      actStatusMap: actStatusMap
      // 删除 activityStatusMap 和 registerStatusMap 的赋值
    }
  },
  created() {
    this.loadActivityTypes()
    this.getList()
  },
  methods: {
    // 加载活动类型选项
    async loadActivityTypes() {
      try {
        const res = await getActivityType({
          delFlag: '0',
          actTypeStatus: 1
        })
        if (res.code === 200) {
          this.activityTypeOptions = res.data.list || []
          // 更新查询配置中的活动类型选项
          const typeQuery = this.queryConfig.queryItem.find(item => item.model === 'typeTitle')
          if (typeQuery) {
            typeQuery.optionList = this.activityTypeOptions
            // 调试信息：检查数据结构
            // console.log('活动类型数据:', this.activityTypeOptions)
          }
        }
      } catch (error) {
        console.error('加载活动类型失败:', error)
      }
    },

    // 新增/编辑/详情/复制
    handleAddOrEdit(type, rowData) {
      this.dialogVisible = true
      if (type === 'add') {
        this.dialogConfig = {
          title: '新增活动',
          type
        }
        this.tableData.pageNum = 1
      } else if (type === 'edit') {
        this.dialogConfig = {
          title: '修改活动',
          formData: rowData,
          type
        }
      } else if (type === 'detail') {
        this.dialogConfig = {
          title: '活动详情',
          formData: rowData,
          type
        }
      } else if (type === 'copy') {
        this.dialogConfig = {
          title: '复制活动',
          formData: rowData, // 保留原始数据，包括ID用于获取详情
          type: 'add', // 类型设为add，但保留原始ID用于获取详情
          isCopy: true // 添加标识表示这是复制操作
        }
      }
    },

    // 上架活动
    handleOnSale(rowData) {
      const msg = `确定对【${rowData.id}】进行上架操作吗？`
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          onSaleActivity(rowData.id).then(res => {
            this.$message({
              type: 'success',
              message: res.msg || '上架成功'
            })
            this.getList()
          }).catch(error => {
            this.$message({
              type: 'error',
              message: error.msg || '上架失败，请重试'
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消上架'
          })
        })
    },

    // 下架活动
    handleDownSale(rowData) {
      const msg = `确定对【${rowData.id}】进行下架操作吗？`
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          downSaleActivity(rowData.id).then(res => {
            this.$message({
              type: 'success',
              message: res.msg || '下架成功'
            })
            this.getList()
          }).catch(error => {
            this.$message({
              type: 'error',
              message: error.msg || '下架失败，请重试'
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消下架'
          })
        })
    },

    // 删除活动
    handleDelete(rowData) {
      const msg = `确定对【${rowData.id}】进行删除操作吗？`
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteActivity(rowData.id).then(res => {
            this.$message({
              type: 'success',
              message: res.msg || '删除成功'
            })
            this.getList()
          }).catch(error => {
            this.$message({
              type: 'error',
              message: error.msg || '删除失败，请重试'
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    /**
     * 查询条件更改
     * @param {Object} queryData
     */
    handleQueryDataChanged: function(queryData) {
      // console.log('demo queryChanged...', queryData);
      this.queryData = queryData
    },
    /**
     * 条件查询时触发该事件
     */
    handleSearch: function(query) {
      // console.log('demo search...', query);
      const _this = this
      _this.queryData = query

      // 请求页数 修改为1
      _this.tableData.pageNum = 1
      // 获取列表，分页数据
      _this.getList()
    },
    // 获取列表
    getList: function() {
      const _this = this
      _this.tableLoading = true
      // 参数赋值，重写部分参数+拼接分页数据
      const params = {
        ...this.queryData,
        delFlag: '0',
        pageNum: this.tableData.pageNum,
        pageSize: this.tableData.pageSize
      }
      // 社区首页
      if (_this.homePageType) {
        params.type = _this.homePageType
      }
      // 活动审核
      if (_this.auditPage) {
        params.listType = 'audit'
      }
      // 时间等特殊参数处理
      if (params.userDateArr) {
        params.registerTimeBegin = params.userDateArr[0]
        params.registerTimeEnd = params.userDateArr[1]
        delete params.userDateArr
      }
      if (params.logOffDateArr) {
        params.cancelStartTime = params.logOffDateArr[0]
        params.cancelEndTime = params.logOffDateArr[1]
        delete params.logOffDateArr
      }
      // 图片机审
      if (params.exampleDateArr) {
        params.auditStartTime = params.exampleDateArr[0]
        params.auditEndTime = params.exampleDateArr[1]
        delete params.exampleDateArr
      }
      // 报名列表
      if (params.signUpDateArr) {
        params.signUpStartTime = params.signUpDateArr[0]
        params.signUpEndTime = params.signUpDateArr[1]
        delete params.signUpDateArr
      }
      // 活动类型创建时间
      if (params.createTimeArr) {
        params.createTimeStart = params.createTimeArr[0]
        params.createTimeEnd = params.createTimeArr[1]
        delete params.createTimeArr
      }
      // 活动类型查询需要添加delFlag参数
      if (_this.queryUrl && _this.queryUrl.includes('activityType')) {
        params.delFlag = '0'
      }
      getTableList(_this.queryUrl, params).then(res => {
        res = res.data
        this.tableData.list = res.list || []
        this.tableData.pageTotal = res.total
      }).finally(() => {
        _this.tableLoading = false
      })
    },
    /**
     * 分页条页码/每页条数改变
     * @param {Object} obj { page: 当前页码, limit: 每页条数 }
     */
    handlePageChange: function(obj) {
      const _this = this
      // TODO 重写页码和size
      this.tableData.pageSize = obj.limit
      this.tableData.pageNum = obj.page
      // 获取列表
      _this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    },
    // 显示活动签到码
    async handleSignCode(rowData) {
      this.currentActivity = {
        ...rowData,
        signCode: `NCDEJ895tr53` // 这里应该是从后端获取的签到码
      }
      this.signCodeVisible = true

      // 等待DOM更新后生成二维码
      this.$nextTick(() => {
        this.generateQRCode()
      })
    },

    // 生成二维码
    async generateQRCode() {
      try {
        const canvas = await QRCode.toCanvas(this.currentActivity.signCode, {
          width: 200,
          height: 200
        })
        const qrContainer = this.$refs.qrcode
        qrContainer.innerHTML = ''
        qrContainer.appendChild(canvas)
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.$message.error('生成二维码失败')
      }
    },

    // 下载签到码
    async downloadSignCode() {
      try {
        const signCodeContent = document.querySelector('.sign-code-content')
        const canvas = await html2canvas(signCodeContent, {
          backgroundColor: '#ffffff',
          scale: 2
        })

        const link = document.createElement('a')
        link.download = `${this.currentActivity.id}-${this.currentActivity.actTitle}.png`
        link.href = canvas.toDataURL()
        link.click()

        this.$message.success('下载成功')
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败')
      }
    },

    // 导出Excel
    async handleExport() {
      try {
        const queryData = this.getQueryData()
        const response = await exportAndDownloadActivity(queryData)

        // 使用返回的文件名或默认文件名
        const fileName = response.filename || '活动列表_' + formatDates(new Date()) + '.xlsx'
        downOrViewFile(response.data, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error(error.message || '导出失败，请重试')
      }
    },

    // 获取查询数据
    getQueryData() {
      const queryData = { ...this.queryData }
      if (queryData.createTimeArr && queryData.createTimeArr.length === 2) {
        queryData.createTimeStart = queryData.createTimeArr[0]
        queryData.createTimeEnd = queryData.createTimeArr[1]
        delete queryData.createTimeArr
      }
      queryData.delFlag = '0'
      return queryData
    },

    // 格式化活动门槛显示
    formatActThreshold(threshold) {
      if (!threshold) return ''
      const thresholds = threshold.split(',')
      const labels = thresholds.map(t => {
        switch (t) {
          case 'ALL': return '所有用户'
          case 'NORMAL': return '普通客户'
          case 'POTENTIAL': return '潜力客户'
          case 'GOLD': return '金卡客户'
          case 'PLATINUM': return '白金卡客户'
          case 'DIAMOND': return '钻石客户'
          case 'PRIVATE': return '私人银行客户'
          default: return t
        }
      })
      return labels.join(', ')
    },

    // 获取活动状态标签类型
    getActivityStatusType(status) {
      switch (String(status)) {
        case '1': return 'info'
        case '2': return 'success'
        case '3': return 'danger'
        default: return ''
      }
    },

    // 获取报名状态标签类型
    getRegisterStatusType(status) {
      switch (String(status)) {
        case '1': return 'info'
        case '2': return 'success'
        case '3': return 'danger'
        default: return ''
      }
    },

    // 获取活动状态文本
    getActivityStatusText(status) {
      switch (String(status)) {
        case '1': return '活动未开始'
        case '2': return '活动进行中'
        case '3': return '活动已结束'
        default: return status || ''
      }
    },

    // 获取报名状态文本
    getRegisterStatusText(status) {
      switch (String(status)) {
        case '1': return '报名未开始'
        case '2': return '报名进行中'
        case '3': return '报名已结束'
        default: return status || ''
      }
    },
    saveSuccess() {
      this.dialogVisible = false
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
// 解决操作列下方小横条问题
::v-deep .el-table__fixed-right {
  height: auto !important;
  bottom: 0 !important;
}

// 可选：调整表格边框样式
::v-deep .el-table {
  border-bottom: none;
}

// 签到码弹窗样式
.sign-code-content {
  text-align: center;

  .qr-code-container {
    margin-bottom: 20px;

    .qr-code {
      display: inline-block;
      padding: 20px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #fff;
    }
  }

  .activity-info {
    text-align: left;

    p {
      margin: 8px 0;
      font-size: 14px;

      strong {
        color: #303133;
      }
    }
  }
}

// 操作按钮样式优化
::v-deep .el-table .el-button--text {
  margin-right: 8px;

  &:last-child {
    margin-right: 0;
  }
}
</style>
